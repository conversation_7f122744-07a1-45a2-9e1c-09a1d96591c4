package com.xy.admin.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.mapper.ConfigHuichuanMapper;
import com.xy.admin.entity.ConfigHuichuan;
import com.xy.admin.service.ConfigHuichuanService;
/**
 * 
 * <AUTHOR>
 * @since 2025/7/28 09:52
 */
@Service
public class ConfigHuichuanServiceImpl extends ServiceImpl<ConfigHuichuanMapper, ConfigHuichuan> implements ConfigHuichuanService{

    @Override
    public int updateByPrimaryKey(ConfigHuichuan record) {
        return baseMapper.updateByPrimaryKey(record);
    }
}
