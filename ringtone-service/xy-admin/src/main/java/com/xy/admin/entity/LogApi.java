package com.xy.admin.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@TableName(value = "log_api")
@Data
public class LogApi implements Serializable {

    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 日志类型，0主调，1被调
     */
    private Integer type;
    /**
     * 
     */
    private String url;
    /**
     * 请求ip
     */
    private String ip;
    /**
     * 方法类型
     */
    private String method;
    /**
     * 代码位置
     */
    private String position;
    /**
     * 请求内容
     */
    private String request;
    /**
     * 回复内容
     */
    private String response;
    /**
     * 花费时间，单位毫秒
     */
    private Integer timing;
    /**
     * 是否发生异常
     */
    private Boolean exception;
    /**
     * 
     */
    private String exceptionName;
    /**
     * 
     */
    private String exceptionTrace;
    /**
     * 
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
