package com.xy.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xy.admin.annotation.DataSource;
import com.xy.admin.entity.ProInfoProduct;
import com.xy.admin.service.ProInfoProductService;
import com.xy.admin.service.CompanyOptionService;
import com.xy.admin.utils.ProInfoProductStatusUtils;
import com.xy.admin.vo.common.CompanyOptionVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductAddVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductEditVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductQueryVO;
import com.xy.admin.vo.proInfoProduct.ProInfoProductVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品推广信息控制层
 *
 * <AUTHOR>
 * @since 2025/7/17
 */
@RestController
@RequestMapping("/proInfoProduct")
public class ProInfoProductController {

	@Autowired
	private ProInfoProductService proInfoProductService;

	@Autowired
	private CompanyOptionService companyOptionService;

	/**
	 * 产品列表查询接口
	 * 支持多字段查询、分页和排序
	 */
	@PostMapping("/list")
	@DataSource("tertiary")
	public ResponseEntity<IPage<ProInfoProductVO>> list(@Valid @RequestBody ProInfoProductQueryVO queryVO) {
		IPage<ProInfoProductVO> result = proInfoProductService.queryPage(queryVO);
		return ResponseEntity.ok(result);
	}

	/**
	 * 单个产品查询接口（通过ID）
	 */
	@GetMapping("/{id}")
	@DataSource("tertiary")
	public ResponseEntity<ProInfoProductVO> getById(@PathVariable Integer id) {
		ProInfoProduct entity = proInfoProductService.getById(id);
		if (entity == null) {
			return ResponseEntity.notFound().build();
		}

		ProInfoProductVO vo = new ProInfoProductVO();
		BeanUtils.copyProperties(entity, vo);
		return ResponseEntity.ok(vo);
	}

	/**
	 * 产品新增接口
	 */
	@PostMapping
	@DataSource("tertiary")
	public ResponseEntity<String> add(@Valid @RequestBody ProInfoProductAddVO addVO) {
		ProInfoProduct entity = new ProInfoProduct();
		BeanUtils.copyProperties(addVO, entity);

		boolean success = proInfoProductService.save(entity);
		if (success) {
			return ResponseEntity.ok("新增成功");
		} else {
			return ResponseEntity.badRequest().body("新增失败");
		}
	}

	/**
	 * 产品修改接口
	 */
	@PutMapping("/{id}")
	@DataSource("tertiary")
	public ResponseEntity<String> update(@PathVariable Integer id, @Valid @RequestBody ProInfoProductEditVO editVO) {
		// 确保ID一致
		editVO.setId(id);

		ProInfoProduct entity = new ProInfoProduct();
		BeanUtils.copyProperties(editVO, entity);

		boolean success = proInfoProductService.updateById(entity);
		if (success) {
			return ResponseEntity.ok("修改成功");
		} else {
			return ResponseEntity.badRequest().body("修改失败");
		}
	}

	/**
	 * 产品删除接口
	 */
	@DeleteMapping("/{id}")
	@DataSource("tertiary")
	public ResponseEntity<String> delete(@PathVariable Integer id) {
		boolean success = proInfoProductService.removeById(id);
		if (success) {
			return ResponseEntity.ok("删除成功");
		} else {
			return ResponseEntity.badRequest().body("删除失败");
		}
	}

	/**
	 * 获取所有选项接口（包括状态选项和公司选项）
	 * 用于前端一次性获取所有下拉框选项
	 */
	@GetMapping("/options")
	@DataSource("tertiary")
	public ResponseEntity<Map<String, Object>> getAllOptions() {
		Map<String, Object> allOptions = new HashMap<>();

		// 产品状态选项
		allOptions.put("productStatus", ProInfoProductStatusUtils.getProductStatusOptions());
		// 开发选项
		allOptions.put("developmentStatus", ProInfoProductStatusUtils.getDevelopmentStatusOptions());

		// 公司选项
		allOptions.put("companyOptions", companyOptionService.getAllCompanyOptions());

		return ResponseEntity.ok(allOptions);
	}

	/**
	 * 获取公司选项接口
	 * 用于前端下拉框显示
	 */
	@GetMapping("/companyOptions")
	@DataSource("tertiary")
	public ResponseEntity<List<CompanyOptionVO>> getCompanyOptions() {
		List<CompanyOptionVO> companyOptions = companyOptionService.getAllCompanyOptions();
		return ResponseEntity.ok(companyOptions);
	}

	/**
	 * 刷新公司选项缓存接口
	 * 用于手动刷新缓存
	 */
	@GetMapping("/refreshCompanyOptions")
	@DataSource("tertiary")
	public ResponseEntity<List<CompanyOptionVO>> refreshCompanyOptions() {
		List<CompanyOptionVO> companyOptions = companyOptionService.refreshCompanyOptions();
		return ResponseEntity.ok(companyOptions);
	}
}
